<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑冥想内容' : '新增冥想内容'"
    width="800px"
    draggable
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入内容标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型">
              <el-option label="冥想" value="meditation" />
              <el-option label="睡眠" value="sleep" />
              <el-option label="自然音" value="nature" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="子类型" prop="sub_type">
            <el-select v-model="formData.sub_type" placeholder="请选择子类型">
              <el-option label="课程" value="course" />
              <el-option label="单集" value="single" />
              <el-option label="系列" value="series" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时长(秒)" prop="duration">
            <el-input-number
              v-model="formData.duration"
              :min="0"
              :max="7200"
              placeholder="请输入时长"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已归档" value="archived" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签" prop="tag_ids">
            <el-select
              v-model="formData.tag_ids"
              multiple
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in tagOptions"
                :key="tag.id"
                :label="tag.name"
                :value="parseInt(tag.id)"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入内容描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="封面URL">
            <el-input
              v-model="formData.cover_url"
              placeholder="请输入封面图片URL"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="音频URL">
            <el-input
              v-model="formData.audio_url"
              placeholder="请输入音频文件URL"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="视频URL">
        <el-input
          v-model="formData.video_url"
          placeholder="请输入视频文件URL"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  createMeditationContent,
  updateMeditationContent,
  getMeditationTagList,
  type MeditationContent,
  type CreateMeditationContentParams,
  type UpdateMeditationContentParams,
  type MeditationTag
} from "@/api/meditation";

interface Props {
  modelValue: boolean;
  data?: MeditationContent;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const tagOptions = ref<MeditationTag[]>([]);

const isEdit = ref(false);

const formData = reactive<CreateMeditationContentParams>({
  title: "",
  description: "",
  type: "meditation",
  sub_type: "single",
  duration: 0,
  tag_ids: [],
  status: "draft",
  cover_url: "",
  audio_url: "",
  video_url: ""
});

const rules: FormRules = {
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  sub_type: [{ required: true, message: "请选择子类型", trigger: "change" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
};

// 获取标签选项
const fetchTagOptions = async () => {
  try {
    const response = await getMeditationTagList({ limit: 100 });
    if (response.code === 200) {
      tagOptions.value = response.data.items;
    }
  } catch (error) {
    console.error("获取标签列表失败:", error);
  }
};

const resetForm = () => {
  Object.assign(formData, {
    title: "",
    description: "",
    type: "meditation",
    sub_type: "single",
    duration: 0,
    tag_ids: [],
    status: "draft",
    cover_url: "",
    audio_url: "",
    video_url: ""
  });
  formRef.value?.clearValidate();
};

const handleClose = () => {
  emit("update:modelValue", false);
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value && props.data) {
      const updateData: UpdateMeditationContentParams = { ...formData };
      const response = await updateMeditationContent(props.data.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      const response = await createMeditationContent(formData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val) {
      isEdit.value = !!props.data;
      if (props.data) {
        Object.assign(formData, {
          title: props.data.title,
          description: props.data.description,
          type: props.data.type,
          sub_type: props.data.sub_type,
          duration: props.data.duration,
          tag_ids: props.data.tags.map(tag => parseInt(tag.id)),
          status: props.data.status,
          cover_url: props.data.cover_url || "",
          audio_url: props.data.audio_url || "",
          video_url: props.data.video_url || ""
        });
      } else {
        resetForm();
      }
    }
  }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

onMounted(() => {
  fetchTagOptions();
});
</script>
