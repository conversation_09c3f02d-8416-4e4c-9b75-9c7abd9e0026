<template>
  <el-dialog
    v-model="visible"
    title="编辑用户等级"
    width="500px"
    draggable
    @close="handleClose"
  >
    <el-form
      v-if="user"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="用户昵称">
        <el-input :value="user.nickname" disabled />
      </el-form-item>
      <el-form-item label="当前等级">
        <el-tag type="info">
          {{ user.meditation_level }}级 - {{ getCurrentLevelName() }}
        </el-tag>
      </el-form-item>
      <el-form-item label="新等级" prop="meditation_level">
        <el-select
          v-model="formData.meditation_level"
          placeholder="请选择新等级"
          style="width: 100%"
        >
          <el-option
            v-for="level in levels"
            :key="level.level"
            :label="`${level.level}级 - ${level.level_name}`"
            :value="level.level"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="调整原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调整等级的原因"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { updateUserLevel } from "@/api/users";
import type { User } from "@/api/users";
import type { UserLevel } from "@/api/userLevels";

interface Props {
  modelValue: boolean;
  user?: User | null;
  levels: UserLevel[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

const formData = reactive({
  meditation_level: 1,
  reason: ""
});

const rules: FormRules = {
  meditation_level: [
    { required: true, message: "请选择新等级", trigger: "change" }
  ],
  reason: [
    { required: true, message: "请输入调整原因", trigger: "blur" },
    { min: 5, max: 200, message: "原因长度在 5 到 200 个字符", trigger: "blur" }
  ]
};

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val && props.user) {
      formData.meditation_level = props.user.meditation_level;
      formData.reason = "";
    }
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const getCurrentLevelName = () => {
  if (!props.user) return "";
  const level = props.levels.find(l => l.level === props.user!.meditation_level);
  return level ? level.level_name : `等级${props.user.meditation_level}`;
};

const handleClose = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

const handleSubmit = async () => {
  if (!props.user) return;
  
  try {
    await formRef.value?.validate();
    
    if (formData.meditation_level === props.user.meditation_level) {
      ElMessage.warning("新等级与当前等级相同");
      return;
    }
    
    loading.value = true;
    const response = await updateUserLevel(props.user.id, {
      meditation_level: formData.meditation_level,
      reason: formData.reason
    });
    
    if (response.code === 200) {
      ElMessage.success("用户等级更新成功");
      emit("success");
      handleClose();
    } else {
      ElMessage.error(response.message || "更新失败");
    }
  } catch (error) {
    console.error("更新用户等级失败:", error);
    ElMessage.error("更新用户等级失败");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 20px;
}
</style>
