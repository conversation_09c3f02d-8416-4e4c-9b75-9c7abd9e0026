<template>
  <el-dialog
    v-model="visible"
    title="等级详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="等级">
          {{ data.level }}级
        </el-descriptions-item>
        <el-descriptions-item label="等级名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="图标">
          <span style="font-size: 24px;">{{ data.icon }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="所需能量">
          {{ data.requiredEnergy }}
        </el-descriptions-item>
        <el-descriptions-item label="所需天数">
          {{ data.requiredDays }}天
        </el-descriptions-item>
        <el-descriptions-item label="特殊能力">
          {{ data.specialAbility || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ data.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="属性加成" :span="2">
          <div class="bonus-stats">
            <div class="bonus-item">
              <span class="bonus-label">成长加成:</span>
              <el-progress 
                :percentage="data.growthBonus" 
                :stroke-width="8"
                :show-text="true"
                color="#409eff"
              />
            </div>
            <div class="bonus-item">
              <span class="bonus-label">美观加成:</span>
              <el-progress 
                :percentage="data.beautyBonus" 
                :stroke-width="8"
                :show-text="true"
                color="#f56c6c"
              />
            </div>
            <div class="bonus-item">
              <span class="bonus-label">耐性加成:</span>
              <el-progress 
                :percentage="data.hardinessBonus" 
                :stroke-width="8"
                :show-text="true"
                color="#67c23a"
              />
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="解锁奖励" :span="2">
          <div class="rewards">
            <el-tag
              v-for="reward in data.unlockRewards"
              :key="reward"
              style="margin-right: 8px; margin-bottom: 4px;"
              type="success"
            >
              {{ reward }}
            </el-tag>
            <span v-if="!data.unlockRewards || data.unlockRewards.length === 0">
              暂无奖励
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface SucculentLevel {
  id: number;
  level: number;
  name: string;
  icon: string;
  requiredEnergy: number;
  requiredDays: number;
  growthBonus: number;
  beautyBonus: number;
  hardinessBonus: number;
  specialAbility: string;
  unlockRewards: string[];
  description: string;
  status: number;
  createTime: string;
}

interface Props {
  modelValue: boolean;
  data?: SucculentLevel | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.bonus-stats {
  .bonus-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .bonus-label {
      width: 80px;
      font-weight: 500;
      margin-right: 12px;
    }
    
    :deep(.el-progress) {
      flex: 1;
    }
  }
}

.rewards {
  line-height: 1.8;
}
</style>
