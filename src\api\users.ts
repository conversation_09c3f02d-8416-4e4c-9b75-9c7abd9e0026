import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 用户数据类型定义
export type User = {
  id: string;
  openid: string;
  unionid?: string;
  nickname: string;
  avatar_url: string;
  meditation_level: number;
  streak_days: number;
  created_at: string;
  updated_at?: string;
  plant_count?: number;
};

// 用户列表响应类型
export type UserListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: User[];
  };
};

// 用户详情响应类型
export type UserDetailResponse = {
  code: number;
  message?: string;
  data: {
    user: User;
    statistics: {
      plant_count: number;
      favorite_count: number;
      meditation_stats: MeditationStat[];
    };
  };
};

// 冥想统计数据类型
export type MeditationStat = {
  id: string;
  user_id: string;
  period_type: string;
  period_date: string;
  meditation_duration: number;
  energy_gained: number;
  tasks_completed: number;
  created_at: string;
};

// 用户统计数据响应类型
export type UserStatisticsResponse = {
  code: number;
  message?: string;
  data: {
    total_users: number;
    level_distribution: Array<{
      meditation_level: number;
      count: string;
    }>;
    recent_registrations: User[];
    active_users: string;
  };
};

// 用户等级更新响应类型
export type UserLevelUpdateResponse = {
  code: number;
  message?: string;
  data: {
    user_id: string;
    old_level: number;
    new_level: number;
    updated_at: string;
  };
};

// 用户列表查询参数
export type UserListParams = {
  page?: number;
  limit?: number;
  search?: string;
  meditation_level?: number;
  start_date?: string;
  end_date?: string;
};

// 用户等级更新参数
export type UpdateUserLevelParams = {
  meditation_level: number;
  reason: string;
};

/** 获取用户列表 */
export const getUserList = (params?: UserListParams) => {
  return http.request<UserListResponse>("get", baseUrlApi("admin/users"), {
    params
  });
};

/** 获取用户详情 */
export const getUserDetail = (userId: string) => {
  return http.request<UserDetailResponse>("get", baseUrlApi(`admin/users/${userId}`));
};

/** 更新用户等级 */
export const updateUserLevel = (userId: string, data: UpdateUserLevelParams) => {
  return http.request<UserLevelUpdateResponse>(
    "put", 
    baseUrlApi(`admin/users/${userId}/level`), 
    { data }
  );
};

/** 获取用户统计数据 */
export const getUserStatistics = () => {
  return http.request<UserStatisticsResponse>("get", baseUrlApi("admin/users/statistics"));
};
