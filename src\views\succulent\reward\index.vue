<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

defineOptions({
  name: "SucculentReward"
});

interface RewardRule {
  id: number;
  name: string;
  type: string;
  trigger: string;
  condition: string;
  rewardType: string;
  rewardValue: number;
  rewardItems: string[];
  dailyLimit: number;
  totalLimit: number;
  priority: number;
  startTime: string;
  endTime: string;
  status: number;
  createTime: string;
}

const loading = ref(false);
const tableData = ref<RewardRule[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

const formData = reactive({
  id: 0,
  name: "",
  type: "每日任务",
  trigger: "冥想完成",
  condition: "",
  rewardType: "能量",
  rewardValue: 10,
  rewardItems: [] as string[],
  dailyLimit: 1,
  totalLimit: 0,
  priority: 1,
  startTime: "",
  endTime: "",
  status: 1
});

const searchForm = reactive({
  name: "",
  type: "",
  status: ""
});

// 模拟数据
const mockData: RewardRule[] = [
  {
    id: 1,
    name: "每日冥想奖励",
    type: "每日任务",
    trigger: "冥想完成",
    condition: "完成任意冥想内容",
    rewardType: "能量",
    rewardValue: 20,
    rewardItems: [],
    dailyLimit: 3,
    totalLimit: 0,
    priority: 1,
    startTime: "",
    endTime: "",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "连续冥想7天",
    type: "成就任务",
    trigger: "连续天数",
    condition: "连续冥想7天",
    rewardType: "道具",
    rewardValue: 0,
    rewardItems: ["营养土", "小花盆"],
    dailyLimit: 0,
    totalLimit: 1,
    priority: 2,
    startTime: "",
    endTime: "",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 3,
    name: "新手礼包",
    type: "新手任务",
    trigger: "注册完成",
    condition: "完成注册",
    rewardType: "混合",
    rewardValue: 100,
    rewardItems: ["基础浇水", "基础施肥", "小花盆"],
    dailyLimit: 0,
    totalLimit: 1,
    priority: 3,
    startTime: "",
    endTime: "",
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 4,
    name: "周末双倍奖励",
    type: "限时活动",
    trigger: "冥想完成",
    condition: "周末完成冥想",
    rewardType: "能量",
    rewardValue: 40,
    rewardItems: [],
    dailyLimit: 5,
    totalLimit: 0,
    priority: 1,
    startTime: "2024-01-20 00:00:00",
    endTime: "2024-01-21 23:59:59",
    status: 1,
    createTime: "2024-01-15 10:00:00"
  }
];

const typeOptions = [
  { label: "每日任务", value: "每日任务" },
  { label: "成就任务", value: "成就任务" },
  { label: "新手任务", value: "新手任务" },
  { label: "限时活动", value: "限时活动" }
];

const triggerOptions = [
  { label: "冥想完成", value: "冥想完成" },
  { label: "连续天数", value: "连续天数" },
  { label: "累计时长", value: "累计时长" },
  { label: "等级提升", value: "等级提升" },
  { label: "注册完成", value: "注册完成" },
  { label: "分享内容", value: "分享内容" }
];

const rewardTypeOptions = [
  { label: "能量", value: "能量" },
  { label: "道具", value: "道具" },
  { label: "混合", value: "混合" }
];

const rewardItemOptions = [
  "基础浇水",
  "基础施肥",
  "营养土",
  "小花盆",
  "装饰花盆",
  "特殊肥料",
  "造型工具",
  "精美花盆",
  "高级肥料",
  "修剪工具"
];

const statusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 }
];

const columns = [
  {
    label: "奖励名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 100
  },
  {
    label: "触发条件",
    prop: "trigger",
    minWidth: 120
  },
  {
    label: "具体条件",
    prop: "condition",
    minWidth: 150
  },
  {
    label: "奖励内容",
    prop: "reward",
    minWidth: 200
  },
  {
    label: "限制次数",
    prop: "limit",
    minWidth: 120
  },
  {
    label: "优先级",
    prop: "priority",
    minWidth: 80
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (
        (!searchForm.name || item.name.includes(searchForm.name)) &&
        (!searchForm.type || item.type === searchForm.type) &&
        (!searchForm.status || item.status.toString() === searchForm.status)
      );
    });
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.name = "";
  searchForm.type = "";
  searchForm.status = "";
  onSearch();
};

const handleAdd = () => {
  dialogTitle.value = "新增奖励规则";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleEdit = (row: RewardRule) => {
  dialogTitle.value = "编辑奖励规则";
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDelete = (row: RewardRule) => {
  ElMessageBox.confirm(`确定要删除奖励规则 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
      ElMessage.success("删除成功");
    }
  });
};

const handleToggleStatus = (row: RewardRule) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}奖励规则 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetFormData = () => {
  formData.id = 0;
  formData.name = "";
  formData.type = "每日任务";
  formData.trigger = "冥想完成";
  formData.condition = "";
  formData.rewardType = "能量";
  formData.rewardValue = 10;
  formData.rewardItems = [];
  formData.dailyLimit = 1;
  formData.totalLimit = 0;
  formData.priority = 1;
  formData.startTime = "";
  formData.endTime = "";
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入奖励名称");
    return;
  }

  if (!formData.condition) {
    ElMessage.warning("请输入具体条件");
    return;
  }

  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      tableData.value[index] = {
        ...formData,
        createTime: tableData.value[index].createTime
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const newRule: RewardRule = {
      ...formData,
      id: Date.now(),
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newRule);
    ElMessage.success("新增成功");
  }

  dialogVisible.value = false;
};

const getPriorityTag = (priority: number) => {
  const priorityMap = {
    1: { type: "danger", text: "高" },
    2: { type: "warning", text: "中" },
    3: { type: "success", text: "低" }
  };
  return priorityMap[priority] || { type: "info", text: "未知" };
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="奖励名称：" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入奖励名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="searchForm.type"
          placeholder="请选择类型"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="奖励机制管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增奖励规则
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #reward="{ row }">
            <div>
              <div
                v-if="row.rewardType === '能量' || row.rewardType === '混合'"
              >
                <el-tag type="success" size="small"
                  >能量 +{{ row.rewardValue }}</el-tag
                >
              </div>
              <div
                v-if="row.rewardType === '道具' || row.rewardType === '混合'"
                class="mt-1"
              >
                <el-tag
                  v-for="item in row.rewardItems"
                  :key="item"
                  size="small"
                  class="mr-1"
                >
                  {{ item }}
                </el-tag>
              </div>
            </div>
          </template>

          <template #limit="{ row }">
            <div class="text-sm">
              <div v-if="row.dailyLimit > 0">每日: {{ row.dailyLimit }}次</div>
              <div v-if="row.totalLimit > 0">总计: {{ row.totalLimit }}次</div>
              <div v-if="row.dailyLimit === 0 && row.totalLimit === 0">
                无限制
              </div>
            </div>
          </template>

          <template #priority="{ row }">
            <el-tag :type="getPriorityTag(row.priority).type" size="small">
              {{ getPriorityTag(row.priority).text }}
            </el-tag>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.status === 1 ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.status === 1 ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? "禁用" : "启用" }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      draggable
    >
      <el-form :model="formData" label-width="120px" label-position="right">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="奖励名称" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入奖励名称"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖励类型">
              <el-select
                v-model="formData.type"
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="触发条件">
              <el-select
                v-model="formData.trigger"
                placeholder="请选择触发条件"
                style="width: 100%"
              >
                <el-option
                  v-for="item in triggerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖励形式">
              <el-select
                v-model="formData.rewardType"
                placeholder="请选择奖励形式"
                style="width: 100%"
              >
                <el-option
                  v-for="item in rewardTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="具体条件" required>
          <el-input
            v-model="formData.condition"
            placeholder="请输入具体的触发条件描述"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item
          v-if="
            formData.rewardType === '能量' || formData.rewardType === '混合'
          "
          label="能量奖励"
        >
          <el-input-number
            v-model="formData.rewardValue"
            :min="1"
            :max="1000"
            controls-position="right"
          />
          <span class="ml-2 text-gray-500">点</span>
        </el-form-item>

        <el-form-item
          v-if="
            formData.rewardType === '道具' || formData.rewardType === '混合'
          "
          label="道具奖励"
        >
          <el-select
            v-model="formData.rewardItems"
            multiple
            placeholder="请选择奖励道具"
            style="width: 100%"
          >
            <el-option
              v-for="item in rewardItemOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="每日限制">
              <el-input-number
                v-model="formData.dailyLimit"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
              <div class="text-xs text-gray-500 mt-1">0表示无限制</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总次数限制">
              <el-input-number
                v-model="formData.totalLimit"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
              <div class="text-xs text-gray-500 mt-1">0表示无限制</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级">
              <el-select v-model="formData.priority" style="width: 100%">
                <el-option label="高" :value="1" />
                <el-option label="中" :value="2" />
                <el-option label="低" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="formData.type === '限时活动'" :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
