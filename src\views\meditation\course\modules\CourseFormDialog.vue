<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑课程' : '创建课程'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="courseForm"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="课程标题" prop="title">
        <el-input
          v-model="courseForm.title"
          placeholder="请输入课程标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="课程描述" prop="description">
        <el-input
          v-model="courseForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入课程描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="课程类型" prop="type">
        <el-select
          v-model="courseForm.type"
          placeholder="请选择课程类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="封面图片">
        <el-input
          v-model="courseForm.cover_url"
          placeholder="请输入封面图片URL"
        />
        <div v-if="courseForm.cover_url" class="mt-2">
          <el-image
            :src="courseForm.cover_url"
            style="width: 200px; height: 120px"
            fit="cover"
            :preview-src-list="[courseForm.cover_url]"
          />
        </div>
      </el-form-item>

      <el-form-item label="课程标签">
        <el-select
          v-model="courseForm.tag_ids"
          multiple
          placeholder="请选择课程标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in allTags"
            :key="tag.id"
            :label="tag.name"
            :value="parseInt(tag.id)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="课程状态" prop="status">
        <el-radio-group v-model="courseForm.status">
          <el-radio value="draft">草稿</el-radio>
          <el-radio value="published">发布</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? "更新课程" : "创建课程" }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, type FormInstance } from "vue";
import { ElMessage } from "element-plus";
import {
  createMeditationContent,
  updateMeditationContent,
  getMeditationContentDetail,
  getMeditationTagList,
  type CreateMeditationContentParams,
  type UpdateMeditationContentParams,
  type MeditationContent,
  type MeditationTag
} from "@/api/meditation";

interface Props {
  modelValue: boolean;
  courseId?: string;
  allTags: MeditationTag[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  courseId: ""
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();

const isEdit = computed(() => !!props.courseId);

const courseForm = reactive<CreateMeditationContentParams>({
  title: "",
  description: "",
  type: "meditation",
  sub_type: "course",
  cover_url: "",
  duration: 0,
  tag_ids: [],
  status: "draft"
});

const formRules = {
  title: [{ required: true, message: "请输入课程标题", trigger: "blur" }],
  description: [{ required: true, message: "请输入课程描述", trigger: "blur" }],
  type: [{ required: true, message: "请选择课程类型", trigger: "change" }],
  status: [{ required: true, message: "请选择课程状态", trigger: "change" }]
};

const typeOptions = [
  { label: "冥想", value: "meditation" },
  { label: "睡眠", value: "sleep" },
  { label: "自然音", value: "nature" }
];

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      resetForm();
      if (isEdit.value) {
        loadCourseDetail();
      }
    }
  },
  { immediate: true }
);

// 监听内部visible状态
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 加载课程详情（编辑模式）
const loadCourseDetail = async () => {
  if (!props.courseId) return;

  loading.value = true;
  try {
    const { data } = await getMeditationContentDetail(props.courseId);
    Object.assign(courseForm, {
      title: data.title,
      description: data.description,
      type: data.type,
      sub_type: data.sub_type,
      cover_url: data.cover_url || "",
      duration: data.duration,
      tag_ids: data.tags.map(tag => parseInt(tag.id)),
      status: data.status
    });
  } catch (error) {
    ElMessage.error("获取课程详情失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 保存课程
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit.value) {
      const updateData: UpdateMeditationContentParams = {
        title: courseForm.title,
        description: courseForm.description,
        type: courseForm.type,
        cover_url: courseForm.cover_url,
        tag_ids: courseForm.tag_ids,
        status: courseForm.status
      };
      await updateMeditationContent(props.courseId!, updateData);
      ElMessage.success("更新课程成功");
    } else {
      await createMeditationContent(courseForm);
      ElMessage.success("创建课程成功");
    }

    handleClose();
    emit("success");
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? "更新课程失败" : "创建课程失败");
      console.error(error);
    }
  } finally {
    saving.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(courseForm, {
    title: "",
    description: "",
    type: "meditation",
    sub_type: "course",
    cover_url: "",
    duration: 0,
    tag_ids: [],
    status: "draft"
  });
};
</script>
